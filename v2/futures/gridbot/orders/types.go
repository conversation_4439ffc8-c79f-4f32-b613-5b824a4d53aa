package orders

import (
	"time"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/adshao/go-binance/v2/futures/gridbot/config"
)

// OrderManager handles order lifecycle management
type OrderManager struct {
	client       *futures.Client
	config       *config.Config
	activeOrders map[int64]*ManagedOrder
	orderHistory []*ManagedOrder
	
	// Channels for order events
	orderEventChan chan *OrderEvent
	stopChan       chan struct{}
}

// ManagedOrder represents an order managed by the order manager
type ManagedOrder struct {
	// Basic order information
	ID              int64                    `json:"id"`
	ClientOrderID   string                   `json:"client_order_id"`
	Symbol          string                   `json:"symbol"`
	Side            futures.SideType         `json:"side"`
	Type            futures.OrderType        `json:"type"`
	Quantity        string                   `json:"quantity"`
	Price           string                   `json:"price"`
	Status          OrderStatus              `json:"status"`
	
	// Grid-specific information
	GridLevelID     int                      `json:"grid_level_id"`
	GridLevel       *config.GridLevel        `json:"grid_level,omitempty"`
	
	// Execution details
	ExecutedQuantity string                  `json:"executed_quantity"`
	CumulativeQuote  string                  `json:"cumulative_quote"`
	AvgPrice         string                  `json:"avg_price"`
	Commission       string                  `json:"commission"`
	CommissionAsset  string                  `json:"commission_asset"`
	
	// Timestamps
	CreatedAt       time.Time                `json:"created_at"`
	UpdatedAt       time.Time                `json:"updated_at"`
	FilledAt        *time.Time               `json:"filled_at,omitempty"`
	CancelledAt     *time.Time               `json:"cancelled_at,omitempty"`
	
	// Retry information
	RetryCount      int                      `json:"retry_count"`
	LastError       string                   `json:"last_error,omitempty"`
	
	// Binance response
	BinanceResponse *futures.CreateOrderResponse `json:"binance_response,omitempty"`
}

// OrderStatus represents the status of a managed order
type OrderStatus string

const (
	OrderStatusPending   OrderStatus = "pending"
	OrderStatusPlaced    OrderStatus = "placed"
	OrderStatusFilled    OrderStatus = "filled"
	OrderStatusCancelled OrderStatus = "cancelled"
	OrderStatusRejected  OrderStatus = "rejected"
	OrderStatusExpired   OrderStatus = "expired"
	OrderStatusError     OrderStatus = "error"
)

// OrderEvent represents events that occur during order management
type OrderEvent struct {
	Type      OrderEventType `json:"type"`
	Timestamp time.Time      `json:"timestamp"`
	Order     *ManagedOrder  `json:"order"`
	Message   string         `json:"message"`
	Error     error          `json:"error,omitempty"`
	Data      interface{}    `json:"data,omitempty"`
}

// OrderEventType represents different types of order events
type OrderEventType string

const (
	OrderEventPlaced     OrderEventType = "order_placed"
	OrderEventFilled     OrderEventType = "order_filled"
	OrderEventCancelled  OrderEventType = "order_cancelled"
	OrderEventRejected   OrderEventType = "order_rejected"
	OrderEventExpired    OrderEventType = "order_expired"
	OrderEventError      OrderEventType = "order_error"
	OrderEventRetry      OrderEventType = "order_retry"
	OrderEventUpdated    OrderEventType = "order_updated"
)

// PlaceOrderRequest represents a request to place an order
type PlaceOrderRequest struct {
	Symbol       string                    `json:"symbol"`
	Side         futures.SideType          `json:"side"`
	Type         futures.OrderType         `json:"type"`
	Quantity     string                    `json:"quantity"`
	Price        string                    `json:"price,omitempty"`
	TimeInForce  futures.TimeInForceType   `json:"time_in_force,omitempty"`
	PositionSide futures.PositionSideType  `json:"position_side,omitempty"`
	ReduceOnly   bool                      `json:"reduce_only,omitempty"`
	
	// Grid-specific fields
	GridLevelID  int                       `json:"grid_level_id"`
	GridLevel    *config.GridLevel         `json:"grid_level,omitempty"`
	
	// Order management options
	MaxRetries   int                       `json:"max_retries,omitempty"`
	RetryDelay   time.Duration             `json:"retry_delay,omitempty"`
	Timeout      time.Duration             `json:"timeout,omitempty"`
}

// PlaceOrderResponse represents the response from placing an order
type PlaceOrderResponse struct {
	Success      bool                         `json:"success"`
	Order        *ManagedOrder                `json:"order,omitempty"`
	Error        error                        `json:"error,omitempty"`
	BinanceResponse *futures.CreateOrderResponse `json:"binance_response,omitempty"`
}

// CancelOrderRequest represents a request to cancel an order
type CancelOrderRequest struct {
	OrderID       int64  `json:"order_id,omitempty"`
	ClientOrderID string `json:"client_order_id,omitempty"`
	Symbol        string `json:"symbol"`
	Reason        string `json:"reason,omitempty"`
}

// CancelOrderResponse represents the response from cancelling an order
type CancelOrderResponse struct {
	Success bool          `json:"success"`
	Order   *ManagedOrder `json:"order,omitempty"`
	Error   error         `json:"error,omitempty"`
}

// OrderQuery represents a query for orders
type OrderQuery struct {
	Symbol        string        `json:"symbol,omitempty"`
	Status        OrderStatus   `json:"status,omitempty"`
	Side          futures.SideType `json:"side,omitempty"`
	GridLevelID   *int          `json:"grid_level_id,omitempty"`
	StartTime     *time.Time    `json:"start_time,omitempty"`
	EndTime       *time.Time    `json:"end_time,omitempty"`
	Limit         int           `json:"limit,omitempty"`
}

// OrderStatistics contains statistics about order management
type OrderStatistics struct {
	TotalOrders      int           `json:"total_orders"`
	ActiveOrders     int           `json:"active_orders"`
	FilledOrders     int           `json:"filled_orders"`
	CancelledOrders  int           `json:"cancelled_orders"`
	RejectedOrders   int           `json:"rejected_orders"`
	ErrorOrders      int           `json:"error_orders"`
	
	TotalVolume      float64       `json:"total_volume"`
	TotalCommission  float64       `json:"total_commission"`
	AvgFillTime      time.Duration `json:"avg_fill_time"`
	SuccessRate      float64       `json:"success_rate"`
	
	// Retry statistics
	TotalRetries     int           `json:"total_retries"`
	AvgRetries       float64       `json:"avg_retries"`
	MaxRetries       int           `json:"max_retries"`
	
	// Performance metrics
	OrdersPerSecond  float64       `json:"orders_per_second"`
	LastOrderTime    *time.Time    `json:"last_order_time,omitempty"`
	
	// Error statistics
	ErrorRate        float64       `json:"error_rate"`
	CommonErrors     map[string]int `json:"common_errors"`
}

// OrderManagerInterface defines the interface for order management
type OrderManagerInterface interface {
	// Order placement
	PlaceOrder(request *PlaceOrderRequest) (*PlaceOrderResponse, error)
	PlaceOrderAsync(request *PlaceOrderRequest) error
	
	// Order cancellation
	CancelOrder(request *CancelOrderRequest) (*CancelOrderResponse, error)
	CancelAllOrders(symbol string) error
	
	// Order queries
	GetOrder(orderID int64) (*ManagedOrder, error)
	GetOrderByClientID(clientOrderID string) (*ManagedOrder, error)
	GetOrders(query *OrderQuery) ([]*ManagedOrder, error)
	GetActiveOrders() []*ManagedOrder
	
	// Order synchronization
	SyncOrder(orderID int64) error
	SyncAllOrders() error
	
	// Statistics and monitoring
	GetStatistics() *OrderStatistics
	GetOrderEvents() <-chan *OrderEvent
	
	// Lifecycle management
	Start() error
	Stop() error
	IsRunning() bool
	
	// Configuration
	UpdateConfig(config *config.Config) error
}

// OrderFilter represents a filter for order queries
type OrderFilter func(*ManagedOrder) bool

// Common order filters
var (
	// FilterByStatus filters orders by status
	FilterByStatus = func(status OrderStatus) OrderFilter {
		return func(order *ManagedOrder) bool {
			return order.Status == status
		}
	}
	
	// FilterBySymbol filters orders by symbol
	FilterBySymbol = func(symbol string) OrderFilter {
		return func(order *ManagedOrder) bool {
			return order.Symbol == symbol
		}
	}
	
	// FilterBySide filters orders by side
	FilterBySide = func(side futures.SideType) OrderFilter {
		return func(order *ManagedOrder) bool {
			return order.Side == side
		}
	}
	
	// FilterByGridLevel filters orders by grid level ID
	FilterByGridLevel = func(gridLevelID int) OrderFilter {
		return func(order *ManagedOrder) bool {
			return order.GridLevelID == gridLevelID
		}
	}
	
	// FilterByTimeRange filters orders by time range
	FilterByTimeRange = func(start, end time.Time) OrderFilter {
		return func(order *ManagedOrder) bool {
			return order.CreatedAt.After(start) && order.CreatedAt.Before(end)
		}
	}
)
