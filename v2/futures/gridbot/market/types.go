package market

import (
	"time"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/adshao/go-binance/v2/futures/gridbot/config"
)

// MarketDataHandler handles real-time market data
type MarketDataHandler struct {
	config       *config.Config
	symbol       string
	
	// WebSocket connections
	priceConn    *WebSocketConnection
	depthConn    *WebSocketConnection
	userConn     *WebSocketConnection
	
	// Data channels
	priceUpdates chan *PriceUpdate
	depthUpdates chan *DepthUpdate
	userUpdates  chan *UserDataUpdate
	
	// Current market state
	currentPrice  *PriceData
	currentDepth  *DepthData
	
	// Connection management
	running      bool
	stopChan     chan struct{}
}

// WebSocketConnection represents a WebSocket connection
type WebSocketConnection struct {
	endpoint     string
	doneC        chan struct{}
	stopC        chan struct{}
	reconnectC   chan struct{}
	connected    bool
	lastPing     time.Time
	reconnectCount int
}

// PriceUpdate represents a price update event
type PriceUpdate struct {
	Symbol    string    `json:"symbol"`
	Price     float64   `json:"price"`
	Timestamp time.Time `json:"timestamp"`
	Source    string    `json:"source"` // ticker, markPrice, etc.
}

// PriceData represents current price information
type PriceData struct {
	Symbol       string    `json:"symbol"`
	Price        float64   `json:"price"`
	MarkPrice    float64   `json:"mark_price"`
	IndexPrice   float64   `json:"index_price"`
	LastPrice    float64   `json:"last_price"`
	BidPrice     float64   `json:"bid_price"`
	AskPrice     float64   `json:"ask_price"`
	Volume24h    float64   `json:"volume_24h"`
	Change24h    float64   `json:"change_24h"`
	ChangePercent24h float64 `json:"change_percent_24h"`
	High24h      float64   `json:"high_24h"`
	Low24h       float64   `json:"low_24h"`
	Timestamp    time.Time `json:"timestamp"`
}

// DepthUpdate represents an order book depth update
type DepthUpdate struct {
	Symbol    string      `json:"symbol"`
	Bids      []PriceLevel `json:"bids"`
	Asks      []PriceLevel `json:"asks"`
	Timestamp time.Time   `json:"timestamp"`
	UpdateID  int64       `json:"update_id"`
}

// DepthData represents current order book depth
type DepthData struct {
	Symbol       string      `json:"symbol"`
	Bids         []PriceLevel `json:"bids"`
	Asks         []PriceLevel `json:"asks"`
	LastUpdateID int64       `json:"last_update_id"`
	Timestamp    time.Time   `json:"timestamp"`
}

// PriceLevel represents a price level in the order book
type PriceLevel struct {
	Price    float64 `json:"price"`
	Quantity float64 `json:"quantity"`
}

// UserDataUpdate represents user data updates (orders, positions, etc.)
type UserDataUpdate struct {
	Type      UserDataType `json:"type"`
	Timestamp time.Time    `json:"timestamp"`
	Data      interface{}  `json:"data"`
}

// UserDataType represents the type of user data update
type UserDataType string

const (
	UserDataTypeOrderUpdate    UserDataType = "order_update"
	UserDataTypePositionUpdate UserDataType = "position_update"
	UserDataTypeAccountUpdate  UserDataType = "account_update"
	UserDataTypeBalanceUpdate  UserDataType = "balance_update"
)

// OrderUpdateData represents order update information
type OrderUpdateData struct {
	Symbol           string                   `json:"symbol"`
	OrderID          int64                    `json:"order_id"`
	ClientOrderID    string                   `json:"client_order_id"`
	Side             futures.SideType         `json:"side"`
	Type             futures.OrderType        `json:"type"`
	Status           futures.OrderStatusType  `json:"status"`
	Quantity         string                   `json:"quantity"`
	Price            string                   `json:"price"`
	ExecutedQuantity string                   `json:"executed_quantity"`
	CumulativeQuote  string                   `json:"cumulative_quote"`
	Commission       string                   `json:"commission"`
	CommissionAsset  string                   `json:"commission_asset"`
	TradeTime        time.Time                `json:"trade_time"`
}

// PositionUpdateData represents position update information
type PositionUpdateData struct {
	Symbol           string  `json:"symbol"`
	PositionAmount   string  `json:"position_amount"`
	EntryPrice       string  `json:"entry_price"`
	MarkPrice        string  `json:"mark_price"`
	UnrealizedPnL    string  `json:"unrealized_pnl"`
	MarginType       string  `json:"margin_type"`
	IsolatedWallet   string  `json:"isolated_wallet"`
	PositionSide     string  `json:"position_side"`
}

// AccountUpdateData represents account update information
type AccountUpdateData struct {
	TotalWalletBalance    string `json:"total_wallet_balance"`
	TotalUnrealizedPnL    string `json:"total_unrealized_pnl"`
	TotalMarginBalance    string `json:"total_margin_balance"`
	TotalInitialMargin    string `json:"total_initial_margin"`
	TotalMaintMargin      string `json:"total_maint_margin"`
	TotalPositionInitialMargin string `json:"total_position_initial_margin"`
	TotalOpenOrderInitialMargin string `json:"total_open_order_initial_margin"`
	MaxWithdrawAmount     string `json:"max_withdraw_amount"`
}

// MarketCondition represents current market conditions
type MarketCondition struct {
	Symbol         string    `json:"symbol"`
	Price          float64   `json:"price"`
	MarkPrice      float64   `json:"mark_price"`
	Timestamp      time.Time `json:"timestamp"`
	Volume24h      float64   `json:"volume_24h"`
	PriceChange24h float64   `json:"price_change_24h"`
	Volatility     float64   `json:"volatility"`
	Trend          TrendDirection `json:"trend"`
	
	// Order book data
	BidPrice       float64   `json:"bid_price"`
	AskPrice       float64   `json:"ask_price"`
	Spread         float64   `json:"spread"`
	SpreadPercent  float64   `json:"spread_percent"`
	
	// Liquidity metrics
	BidDepth       float64   `json:"bid_depth"`
	AskDepth       float64   `json:"ask_depth"`
	TotalDepth     float64   `json:"total_depth"`
	
	// Technical indicators
	RSI            float64   `json:"rsi,omitempty"`
	MACD           float64   `json:"macd,omitempty"`
	BollingerUpper float64   `json:"bollinger_upper,omitempty"`
	BollingerLower float64   `json:"bollinger_lower,omitempty"`
}

// TrendDirection represents market trend direction
type TrendDirection string

const (
	TrendUp       TrendDirection = "up"
	TrendDown     TrendDirection = "down"
	TrendSideways TrendDirection = "sideways"
	TrendUnknown  TrendDirection = "unknown"
)

// MarketDataEvent represents market data events
type MarketDataEvent struct {
	Type      MarketDataEventType `json:"type"`
	Timestamp time.Time           `json:"timestamp"`
	Symbol    string              `json:"symbol"`
	Data      interface{}         `json:"data"`
	Message   string              `json:"message"`
}

// MarketDataEventType represents different types of market data events
type MarketDataEventType string

const (
	MarketDataEventPriceUpdate     MarketDataEventType = "price_update"
	MarketDataEventDepthUpdate     MarketDataEventType = "depth_update"
	MarketDataEventUserDataUpdate  MarketDataEventType = "user_data_update"
	MarketDataEventConnectionLost  MarketDataEventType = "connection_lost"
	MarketDataEventConnectionRestored MarketDataEventType = "connection_restored"
	MarketDataEventError           MarketDataEventType = "error"
)

// ConnectionStatus represents WebSocket connection status
type ConnectionStatus string

const (
	ConnectionStatusDisconnected ConnectionStatus = "disconnected"
	ConnectionStatusConnecting   ConnectionStatus = "connecting"
	ConnectionStatusConnected    ConnectionStatus = "connected"
	ConnectionStatusReconnecting ConnectionStatus = "reconnecting"
	ConnectionStatusError        ConnectionStatus = "error"
)

// MarketDataHandlerInterface defines the interface for market data handling
type MarketDataHandlerInterface interface {
	// Lifecycle management
	Start() error
	Stop() error
	IsRunning() bool
	
	// Data subscriptions
	SubscribePriceUpdates() error
	SubscribeDepthUpdates() error
	SubscribeUserDataUpdates() error
	
	// Data access
	GetCurrentPrice() *PriceData
	GetCurrentDepth() *DepthData
	GetMarketCondition() *MarketCondition
	
	// Event channels
	GetPriceUpdates() <-chan *PriceUpdate
	GetDepthUpdates() <-chan *DepthUpdate
	GetUserDataUpdates() <-chan *UserDataUpdate
	GetMarketDataEvents() <-chan *MarketDataEvent
	
	// Connection management
	GetConnectionStatus() map[string]ConnectionStatus
	Reconnect() error
	
	// Configuration
	UpdateConfig(config *config.Config) error
	SetSymbol(symbol string) error
}

// MarketDataStatistics contains statistics about market data
type MarketDataStatistics struct {
	// Connection statistics
	TotalConnections    int           `json:"total_connections"`
	ActiveConnections   int           `json:"active_connections"`
	TotalReconnects     int           `json:"total_reconnects"`
	LastReconnectTime   *time.Time    `json:"last_reconnect_time,omitempty"`
	
	// Data statistics
	TotalPriceUpdates   int64         `json:"total_price_updates"`
	TotalDepthUpdates   int64         `json:"total_depth_updates"`
	TotalUserUpdates    int64         `json:"total_user_updates"`
	
	// Performance metrics
	AvgLatency          time.Duration `json:"avg_latency"`
	MaxLatency          time.Duration `json:"max_latency"`
	MinLatency          time.Duration `json:"min_latency"`
	UpdatesPerSecond    float64       `json:"updates_per_second"`
	
	// Error statistics
	TotalErrors         int           `json:"total_errors"`
	ConnectionErrors    int           `json:"connection_errors"`
	DataErrors          int           `json:"data_errors"`
	LastErrorTime       *time.Time    `json:"last_error_time,omitempty"`
	LastError           string        `json:"last_error,omitempty"`
	
	// Uptime
	StartTime           time.Time     `json:"start_time"`
	Uptime              time.Duration `json:"uptime"`
	UptimePercentage    float64       `json:"uptime_percentage"`
}
