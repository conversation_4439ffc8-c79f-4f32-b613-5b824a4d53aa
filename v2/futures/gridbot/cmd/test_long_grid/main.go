package main

import (
	"fmt"
	"log"
	"time"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/adshao/go-binance/v2/futures/gridbot/config"
	"github.com/adshao/go-binance/v2/futures/gridbot/market"
	"github.com/adshao/go-binance/v2/futures/gridbot/orders"
	"github.com/adshao/go-binance/v2/futures/gridbot/strategy"
)

func main() {
	fmt.Printf("🚀 LONG GRID STRATEGY INTEGRATION TEST\n")
	fmt.Printf("======================================\n")

	// Load long grid configuration
	cfg, err := config.LoadConfig("config/examples/long_grid_test.yaml")
	if err != nil {
		log.Fatalf("❌ Failed to load long grid config: %v", err)
	}

	fmt.Printf("✅ Configuration loaded: %s strategy\n", cfg.Grid.Type)
	fmt.Printf("📊 Symbol: %s | Leverage: %dx | Margin: %.0f USDT\n", 
		cfg.Trading.Symbol, cfg.Trading.Leverage, cfg.Trading.InitialMargin)
	fmt.Printf("📈 Grid Range: %.0f - %.0f | Count: %d | Type: %s\n", 
		cfg.Grid.PriceRange.Lower, cfg.Grid.PriceRange.Upper, cfg.Grid.GridCount, cfg.Grid.Calculation)

	// Set testnet
	if cfg.API.Testnet {
		futures.UseTestnet = true
		fmt.Printf("🔧 Using Binance Testnet\n")
	}

	// Create client and components
	client := futures.NewClient(cfg.API.APIKey, cfg.API.SecretKey)
	marketHandler := market.NewMarketDataHandler(client, cfg, cfg.Trading.Symbol)
	orderManager := orders.NewOrderManager(client, cfg)
	gridStrategy := strategy.NewGridStrategy(cfg)

	// Test 1: Long Grid Level Analysis
	fmt.Printf("\n=== TEST 1: LONG GRID LEVEL ANALYSIS ===\n")
	
	calculator := strategy.NewGridCalculator(cfg)
	result, err := calculator.CalculateGridLevels()
	if err != nil {
		log.Fatalf("❌ Grid calculation failed: %v", err)
	}

	// For long strategy, we expect more buy orders in lower 70% of range
	priceRange := cfg.Grid.PriceRange.Upper - cfg.Grid.PriceRange.Lower
	buyThreshold := cfg.Grid.PriceRange.Lower + (priceRange * 0.7) // 70% threshold
	
	buyOrdersLower := 0  // Buy orders in lower 70%
	sellOrdersUpper := 0 // Sell orders in upper 30%
	totalBuyQty := 0.0
	totalSellQty := 0.0
	buyQtyLower := 0.0
	sellQtyUpper := 0.0

	fmt.Printf("📊 Long Strategy Analysis (Buy threshold: %.2f):\n", buyThreshold)
	fmt.Printf("📋 Grid Levels Analysis:\n")

	for i, level := range result.Levels {
		side := "BUY"
		region := "LOWER"
		
		if level.Side == futures.SideTypeSell {
			side = "SELL"
			totalSellQty += level.Quantity
			if level.Price > buyThreshold {
				sellOrdersUpper++
				sellQtyUpper += level.Quantity
				region = "UPPER"
			}
		} else {
			totalBuyQty += level.Quantity
			if level.Price <= buyThreshold {
				buyOrdersLower++
				buyQtyLower += level.Quantity
			} else {
				region = "UPPER"
			}
		}
		
		fmt.Printf("  Level %2d: %s  | Price: %8.2f | Qty: %.6f | %s region\n", 
			i+1, side, level.Price, level.Quantity, region)
	}

	// Validate long strategy characteristics
	fmt.Printf("\n📈 LONG STRATEGY VALIDATION:\n")
	fmt.Printf("  Buy Orders (Lower 70%%): %d\n", buyOrdersLower)
	fmt.Printf("  Sell Orders (Upper 30%%): %d\n", sellOrdersUpper)
	fmt.Printf("  Total Buy Quantity: %.6f | Total Sell Quantity: %.6f\n", totalBuyQty, totalSellQty)
	fmt.Printf("  Buy Qty (Lower): %.6f | Sell Qty (Upper): %.6f\n", buyQtyLower, sellQtyUpper)
	
	// Check long strategy characteristics
	buyHeavy := totalBuyQty > totalSellQty
	concentratedBuying := buyOrdersLower > sellOrdersUpper
	
	fmt.Printf("  Buy-Heavy Distribution: %v\n", buyHeavy)
	fmt.Printf("  Concentrated Lower Buying: %v\n", concentratedBuying)
	
	if buyHeavy {
		fmt.Printf("  ✅ Strategy is buy-heavy (suitable for uptrends)\n")
	} else {
		fmt.Printf("  ⚠️  Strategy may not be optimally buy-heavy\n")
	}
	
	if concentratedBuying {
		fmt.Printf("  ✅ More buy orders in lower price region\n")
	} else {
		fmt.Printf("  ⚠️  Buy orders may not be concentrated in lower region\n")
	}

	// Test 2: Component Integration
	fmt.Printf("\n=== TEST 2: COMPONENT INTEGRATION ===\n")
	
	// Start market handler
	err = marketHandler.Start()
	if err != nil {
		log.Fatalf("❌ Failed to start market handler: %v", err)
	}
	fmt.Printf("✅ Market data handler started\n")

	// Subscribe to data feeds
	err = marketHandler.SubscribePriceUpdates()
	if err != nil {
		log.Fatalf("❌ Failed to subscribe to price updates: %v", err)
	}
	
	err = marketHandler.SubscribeDepthUpdates()
	if err != nil {
		log.Fatalf("❌ Failed to subscribe to depth updates: %v", err)
	}
	
	fmt.Printf("✅ WebSocket subscriptions active\n")

	// Start order manager
	err = orderManager.Start()
	if err != nil {
		log.Fatalf("❌ Failed to start order manager: %v", err)
	}
	fmt.Printf("✅ Order manager started\n")

	// Initialize and start strategy
	err = gridStrategy.Initialize()
	if err != nil {
		log.Fatalf("❌ Failed to initialize strategy: %v", err)
	}
	
	err = gridStrategy.Start()
	if err != nil {
		log.Fatalf("❌ Failed to start strategy: %v", err)
	}
	fmt.Printf("✅ Long grid strategy initialized and started\n")

	// Test 3: Market Trend Analysis
	fmt.Printf("\n=== TEST 3: MARKET TREND ANALYSIS ===\n")
	fmt.Printf("⏱️  Running for 15 seconds to analyze market trends...\n")

	updateCount := 0
	priceUpdates := 0
	depthUpdates := 0
	trendUpCount := 0
	trendDownCount := 0
	trendSidewaysCount := 0
	
	timeout := time.After(15 * time.Second)
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			goto testComplete
			
		case <-ticker.C:
			// Process market data
			condition := marketHandler.GetMarketCondition()
			if condition != nil {
				err = gridStrategy.Update(condition)
				if err != nil {
					fmt.Printf("⚠️  Strategy update error: %v\n", err)
				} else {
					updateCount++
					
					// Count trend directions
					switch condition.Trend {
					case market.TrendUp:
						trendUpCount++
					case market.TrendDown:
						trendDownCount++
					case market.TrendSideways:
						trendSidewaysCount++
					}
					
					fmt.Printf("📊 Update #%d - Price: %.2f | Trend: %s | Vol: %.2f%%\n", 
						updateCount, condition.Price, condition.Trend, condition.Volatility)
				}
			}
			
			// Count data updates
			select {
			case <-marketHandler.GetPriceUpdates():
				priceUpdates++
			default:
			}
			
			select {
			case <-marketHandler.GetDepthUpdates():
				depthUpdates++
			default:
			}
		}
	}

testComplete:
	// Test 4: Long Strategy Performance Analysis
	fmt.Printf("\n=== TEST 4: LONG STRATEGY PERFORMANCE ===\n")
	
	stats := gridStrategy.GetStatistics()
	fmt.Printf("📈 Strategy Statistics:\n")
	fmt.Printf("  Status: %s\n", gridStrategy.GetStatus())
	fmt.Printf("  Runtime: %s\n", stats.RunDuration)
	fmt.Printf("  Active Grids: %d\n", stats.ActiveGrids)
	fmt.Printf("  Current Price: %.2f\n", stats.CurrentPrice)
	
	// Analyze trend suitability for long strategy
	fmt.Printf("📊 Market Trend Analysis:\n")
	fmt.Printf("  Uptrend Periods: %d\n", trendUpCount)
	fmt.Printf("  Downtrend Periods: %d\n", trendDownCount)
	fmt.Printf("  Sideways Periods: %d\n", trendSidewaysCount)
	
	if trendUpCount >= trendDownCount {
		fmt.Printf("  ✅ Market showing upward bias - suitable for long strategy\n")
	} else {
		fmt.Printf("  ⚠️  Market showing downward bias - may challenge long strategy\n")
	}
	
	// Check if price is in favorable region for long strategy
	currentPrice := stats.CurrentPrice
	if currentPrice > 0 {
		if currentPrice <= buyThreshold {
			fmt.Printf("  ✅ Current price in lower region - good for accumulation\n")
		} else {
			fmt.Printf("  📈 Current price in upper region - potential profit-taking zone\n")
		}
	}

	// Test 5: Risk Management for Long Strategy
	fmt.Printf("\n=== TEST 5: LONG STRATEGY RISK MANAGEMENT ===\n")
	
	orderStats := orderManager.GetStatistics()
	fmt.Printf("📋 Order Manager Statistics:\n")
	fmt.Printf("  Total Orders: %d\n", orderStats.TotalOrders)
	fmt.Printf("  Active Orders: %d\n", orderStats.ActiveOrders)
	fmt.Printf("  Success Rate: %.2f%%\n", orderStats.SuccessRate)
	
	// Check risk parameters specific to long strategy
	fmt.Printf("🛡️  Risk Management Check:\n")
	fmt.Printf("  Stop Loss Price: %.2f (%.1f%% below current)\n", 
		cfg.Risk.StopLoss.PriceTrigger, 
		((stats.CurrentPrice-cfg.Risk.StopLoss.PriceTrigger)/stats.CurrentPrice)*100)
	fmt.Printf("  Take Profit Target: %.1f%% ROI\n", cfg.Risk.TakeProfit.ROIPercentage)
	fmt.Printf("  Max Drawdown Limit: %.1f%%\n", cfg.Risk.MaxDrawdown)
	
	if stats.CurrentPrice > cfg.Risk.StopLoss.PriceTrigger {
		fmt.Printf("  ✅ Current price above stop loss level\n")
	} else {
		fmt.Printf("  ⚠️  Current price near or below stop loss level\n")
	}

	// Cleanup
	fmt.Printf("\n=== CLEANUP ===\n")
	
	err = gridStrategy.Stop()
	if err != nil {
		fmt.Printf("⚠️  Error stopping strategy: %v\n", err)
	} else {
		fmt.Printf("✅ Strategy stopped\n")
	}
	
	err = orderManager.Stop()
	if err != nil {
		fmt.Printf("⚠️  Error stopping order manager: %v\n", err)
	} else {
		fmt.Printf("✅ Order manager stopped\n")
	}
	
	err = marketHandler.Stop()
	if err != nil {
		fmt.Printf("⚠️  Error stopping market handler: %v\n", err)
	} else {
		fmt.Printf("✅ Market handler stopped\n")
	}

	// Test Summary
	fmt.Printf("\n🚀 LONG GRID STRATEGY TEST COMPLETED\n")
	fmt.Printf("====================================\n")
	
	testsPassed := 0
	totalTests := 5
	
	if result != nil && buyHeavy && concentratedBuying {
		fmt.Printf("✅ Long grid calculation: PASSED\n")
		testsPassed++
	} else {
		fmt.Printf("❌ Long grid calculation: FAILED\n")
	}
	
	if marketHandler.IsRunning() || updateCount > 0 {
		fmt.Printf("✅ Component integration: PASSED\n")
		testsPassed++
	} else {
		fmt.Printf("❌ Component integration: FAILED\n")
	}
	
	if updateCount > 0 {
		fmt.Printf("✅ Market trend analysis: PASSED (%d updates)\n", updateCount)
		testsPassed++
	} else {
		fmt.Printf("❌ Market trend analysis: FAILED\n")
	}
	
	if gridStrategy.GetStatus() != "" {
		fmt.Printf("✅ Long strategy performance: PASSED\n")
		testsPassed++
	} else {
		fmt.Printf("❌ Long strategy performance: FAILED\n")
	}
	
	if orderStats.TotalOrders >= 0 {
		fmt.Printf("✅ Risk management: PASSED\n")
		testsPassed++
	} else {
		fmt.Printf("❌ Risk management: FAILED\n")
	}
	
	fmt.Printf("\n📊 TEST RESULTS: %d/%d PASSED\n", testsPassed, totalTests)
	
	if testsPassed == totalTests {
		fmt.Printf("🎉 ALL TESTS PASSED!\n")
	} else {
		fmt.Printf("⚠️  SOME TESTS FAILED\n")
	}
	
	fmt.Printf("\n🚀 LONG STRATEGY CHARACTERISTICS VERIFIED:\n")
	fmt.Printf("   - Buy-heavy order distribution\n")
	fmt.Printf("   - More buy orders in lower price regions\n")
	fmt.Printf("   - Optimized for upward trending markets\n")
	fmt.Printf("   - Accumulates on dips, sells on rallies\n")
}
