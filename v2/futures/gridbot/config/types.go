package config

import (
	"time"

	"github.com/adshao/go-binance/v2/futures"
)

// Config represents the complete grid trading bot configuration
type Config struct {
	// API Configuration
	API APIConfig `yaml:"api"`

	// Trading Configuration
	Trading TradingConfig `yaml:"trading"`

	// Grid Strategy Configuration
	Grid GridConfig `yaml:"grid"`

	// Risk Management Configuration
	Risk RiskConfig `yaml:"risk_management"`

	// Monitoring Configuration
	Monitoring MonitoringConfig `yaml:"monitoring"`

	// Advanced Configuration
	Advanced AdvancedConfig `yaml:"advanced"`
}

// APIConfig contains Binance API configuration
type APIConfig struct {
	APIKey    string `yaml:"api_key"`
	SecretKey string `yaml:"secret_key"`
	Testnet   bool   `yaml:"testnet"`
	Proxy     string `yaml:"proxy,omitempty"`
}

// TradingConfig contains basic trading parameters
type TradingConfig struct {
	Symbol        string  `yaml:"symbol"`
	Leverage      int     `yaml:"leverage"`
	InitialMargin float64 `yaml:"initial_margin"`
	DryRun        bool    `yaml:"dry_run"`
}

// GridConfig contains grid strategy configuration
type GridConfig struct {
	Type        GridType        `yaml:"type"`
	Calculation CalculationType `yaml:"calculation"`
	PriceRange  PriceRange      `yaml:"price_range"`
	GridCount   int             `yaml:"grid_count"`
	
	// Optional: Custom grid levels (overrides calculation)
	CustomLevels []float64 `yaml:"custom_levels,omitempty"`
}

// GridType represents the grid trading direction
type GridType string

const (
	GridTypeNeutral GridType = "neutral"
	GridTypeLong    GridType = "long"
	GridTypeShort   GridType = "short"
)

// CalculationType represents the grid calculation method
type CalculationType string

const (
	CalculationArithmetic CalculationType = "arithmetic"
	CalculationGeometric  CalculationType = "geometric"
)

// PriceRange defines the trading range boundaries
type PriceRange struct {
	Lower float64 `yaml:"lower"`
	Upper float64 `yaml:"upper"`
}

// RiskConfig contains risk management settings
type RiskConfig struct {
	StopLoss     StopLossConfig     `yaml:"stop_loss"`
	TakeProfit   TakeProfitConfig   `yaml:"take_profit"`
	MaxDrawdown  float64            `yaml:"max_drawdown"`
	MaxLoss      float64            `yaml:"max_loss,omitempty"`
	MarginBuffer float64            `yaml:"margin_buffer"`
	Emergency    EmergencyConfig    `yaml:"emergency"`
}

// StopLossConfig defines stop loss triggers
type StopLossConfig struct {
	Enabled      bool    `yaml:"enabled"`
	PriceTrigger float64 `yaml:"price_trigger,omitempty"`
	PnLTrigger   float64 `yaml:"pnl_trigger,omitempty"`
}

// TakeProfitConfig defines take profit triggers
type TakeProfitConfig struct {
	Enabled       bool    `yaml:"enabled"`
	PriceTrigger  float64 `yaml:"price_trigger,omitempty"`
	ROIPercentage float64 `yaml:"roi_percentage,omitempty"`
	PnLTrigger    float64 `yaml:"pnl_trigger,omitempty"`
}

// EmergencyConfig defines emergency stop conditions
type EmergencyConfig struct {
	Enabled           bool    `yaml:"enabled"`
	MaxConsecutiveLoss int    `yaml:"max_consecutive_loss"`
	LiquidationBuffer float64 `yaml:"liquidation_buffer"`
}

// MonitoringConfig contains logging and monitoring settings
type MonitoringConfig struct {
	LogLevel      string        `yaml:"log_level"`
	LogFile       string        `yaml:"log_file,omitempty"`
	MetricsEnabled bool         `yaml:"metrics_enabled"`
	TradeHistory  bool          `yaml:"trade_history"`
	WebhookURL    string        `yaml:"webhook_url,omitempty"`
	UpdateInterval time.Duration `yaml:"update_interval"`
}

// AdvancedConfig contains advanced trading parameters
type AdvancedConfig struct {
	OrderTimeout       time.Duration `yaml:"order_timeout"`
	MaxRetries         int           `yaml:"max_retries"`
	RetryDelay         time.Duration `yaml:"retry_delay"`
	PriceDeviation     float64       `yaml:"price_deviation"`
	MinOrderSize       float64       `yaml:"min_order_size"`
	MaxOrderSize       float64       `yaml:"max_order_size,omitempty"`
	RebalanceInterval  time.Duration `yaml:"rebalance_interval"`
	HealthCheckInterval time.Duration `yaml:"health_check_interval"`
	
	// WebSocket Configuration
	WebSocket WebSocketConfig `yaml:"websocket"`
}

// WebSocketConfig contains WebSocket connection settings
type WebSocketConfig struct {
	ReconnectInterval time.Duration `yaml:"reconnect_interval"`
	PingInterval      time.Duration `yaml:"ping_interval"`
	MaxReconnects     int           `yaml:"max_reconnects"`
	BufferSize        int           `yaml:"buffer_size"`
}

// GridLevel represents a single grid level
type GridLevel struct {
	Price     float64                   `json:"price"`
	Side      futures.SideType          `json:"side"`
	Quantity  float64                   `json:"quantity"`
	OrderID   int64                     `json:"order_id,omitempty"`
	Status    GridLevelStatus           `json:"status"`
	CreatedAt time.Time                 `json:"created_at"`
	UpdatedAt time.Time                 `json:"updated_at"`
}

// GridLevelStatus represents the status of a grid level
type GridLevelStatus string

const (
	GridLevelStatusPending   GridLevelStatus = "pending"
	GridLevelStatusActive    GridLevelStatus = "active"
	GridLevelStatusFilled    GridLevelStatus = "filled"
	GridLevelStatusCancelled GridLevelStatus = "cancelled"
	GridLevelStatusError     GridLevelStatus = "error"
)

// BotStatus represents the current status of the grid bot
type BotStatus string

const (
	BotStatusStopped  BotStatus = "stopped"
	BotStatusStarting BotStatus = "starting"
	BotStatusRunning  BotStatus = "running"
	BotStatusPaused   BotStatus = "paused"
	BotStatusStopping BotStatus = "stopping"
	BotStatusError    BotStatus = "error"
)

// Statistics contains bot performance statistics
type Statistics struct {
	StartTime         time.Time `json:"start_time"`
	RunTime           time.Duration `json:"run_time"`
	TotalTrades       int       `json:"total_trades"`
	ProfitableTrades  int       `json:"profitable_trades"`
	TotalPnL          float64   `json:"total_pnl"`
	TotalFees         float64   `json:"total_fees"`
	NetPnL            float64   `json:"net_pnl"`
	ROI               float64   `json:"roi"`
	MaxDrawdown       float64   `json:"max_drawdown"`
	WinRate           float64   `json:"win_rate"`
	AvgTradeSize      float64   `json:"avg_trade_size"`
	GridEfficiency    float64   `json:"grid_efficiency"`
}

// Default configuration values
const (
	DefaultLeverage           = 1
	DefaultGridCount          = 10
	DefaultMaxDrawdown        = 10.0
	DefaultMarginBuffer       = 20.0
	DefaultOrderTimeout       = 30 * time.Second
	DefaultMaxRetries         = 3
	DefaultRetryDelay         = 5 * time.Second
	DefaultPriceDeviation     = 0.1
	DefaultRebalanceInterval  = 5 * time.Minute
	DefaultHealthCheckInterval = 30 * time.Second
	DefaultUpdateInterval     = 10 * time.Second
	DefaultReconnectInterval  = 5 * time.Second
	DefaultPingInterval       = 20 * time.Second
	DefaultMaxReconnects      = 10
	DefaultBufferSize         = 1000
)
