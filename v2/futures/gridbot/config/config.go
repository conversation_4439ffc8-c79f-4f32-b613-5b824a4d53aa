package config

import (
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// LoadConfig loads configuration from a YAML file
func LoadConfig(configPath string) (*Config, error) {
	// Check if file exists
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("configuration file not found: %s", configPath)
	}

	// Read file
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read configuration file: %w", err)
	}

	// Parse YAML
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse configuration file: %w", err)
	}

	// Apply defaults
	applyDefaults(&config)

	// Validate configuration
	if err := ValidateConfig(&config); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	return &config, nil
}

// SaveConfig saves configuration to a YAML file
func SaveConfig(config *Config, configPath string) error {
	// Create directory if it doesn't exist
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	// Marshal to YAML
	data, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("failed to marshal configuration: %w", err)
	}

	// Write to file
	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write configuration file: %w", err)
	}

	return nil
}

// applyDefaults applies default values to configuration
func applyDefaults(config *Config) {
	// Trading defaults
	if config.Trading.Leverage == 0 {
		config.Trading.Leverage = DefaultLeverage
	}

	// Grid defaults
	if config.Grid.GridCount == 0 {
		config.Grid.GridCount = DefaultGridCount
	}
	if config.Grid.Type == "" {
		config.Grid.Type = GridTypeNeutral
	}
	if config.Grid.Calculation == "" {
		config.Grid.Calculation = CalculationArithmetic
	}

	// Risk management defaults
	if config.Risk.MaxDrawdown == 0 {
		config.Risk.MaxDrawdown = DefaultMaxDrawdown
	}
	if config.Risk.MarginBuffer == 0 {
		config.Risk.MarginBuffer = DefaultMarginBuffer
	}

	// Monitoring defaults
	if config.Monitoring.LogLevel == "" {
		config.Monitoring.LogLevel = "info"
	}
	if config.Monitoring.UpdateInterval == 0 {
		config.Monitoring.UpdateInterval = DefaultUpdateInterval
	}

	// Advanced defaults
	if config.Advanced.OrderTimeout == 0 {
		config.Advanced.OrderTimeout = DefaultOrderTimeout
	}
	if config.Advanced.MaxRetries == 0 {
		config.Advanced.MaxRetries = DefaultMaxRetries
	}
	if config.Advanced.RetryDelay == 0 {
		config.Advanced.RetryDelay = DefaultRetryDelay
	}
	if config.Advanced.PriceDeviation == 0 {
		config.Advanced.PriceDeviation = DefaultPriceDeviation
	}
	if config.Advanced.RebalanceInterval == 0 {
		config.Advanced.RebalanceInterval = DefaultRebalanceInterval
	}
	if config.Advanced.HealthCheckInterval == 0 {
		config.Advanced.HealthCheckInterval = DefaultHealthCheckInterval
	}

	// WebSocket defaults
	if config.Advanced.WebSocket.ReconnectInterval == 0 {
		config.Advanced.WebSocket.ReconnectInterval = DefaultReconnectInterval
	}
	if config.Advanced.WebSocket.PingInterval == 0 {
		config.Advanced.WebSocket.PingInterval = DefaultPingInterval
	}
	if config.Advanced.WebSocket.MaxReconnects == 0 {
		config.Advanced.WebSocket.MaxReconnects = DefaultMaxReconnects
	}
	if config.Advanced.WebSocket.BufferSize == 0 {
		config.Advanced.WebSocket.BufferSize = DefaultBufferSize
	}
}

// LoadFromEnvironment loads configuration from environment variables
func LoadFromEnvironment() (*Config, error) {
	config := &Config{
		API: APIConfig{
			APIKey:    os.Getenv("BINANCE_API_KEY"),
			SecretKey: os.Getenv("BINANCE_SECRET_KEY"),
			Testnet:   os.Getenv("BINANCE_TESTNET") == "true",
			Proxy:     os.Getenv("BINANCE_PROXY"),
		},
	}

	// Apply defaults
	applyDefaults(config)

	return config, nil
}

// MergeConfigs merges two configurations, with the second one taking precedence
func MergeConfigs(base, override *Config) *Config {
	merged := *base

	// Merge API config
	if override.API.APIKey != "" {
		merged.API.APIKey = override.API.APIKey
	}
	if override.API.SecretKey != "" {
		merged.API.SecretKey = override.API.SecretKey
	}
	if override.API.Proxy != "" {
		merged.API.Proxy = override.API.Proxy
	}

	// Merge Trading config
	if override.Trading.Symbol != "" {
		merged.Trading.Symbol = override.Trading.Symbol
	}
	if override.Trading.Leverage != 0 {
		merged.Trading.Leverage = override.Trading.Leverage
	}
	if override.Trading.InitialMargin != 0 {
		merged.Trading.InitialMargin = override.Trading.InitialMargin
	}

	// Merge Grid config
	if override.Grid.Type != "" {
		merged.Grid.Type = override.Grid.Type
	}
	if override.Grid.Calculation != "" {
		merged.Grid.Calculation = override.Grid.Calculation
	}
	if override.Grid.GridCount != 0 {
		merged.Grid.GridCount = override.Grid.GridCount
	}
	if override.Grid.PriceRange.Lower != 0 {
		merged.Grid.PriceRange.Lower = override.Grid.PriceRange.Lower
	}
	if override.Grid.PriceRange.Upper != 0 {
		merged.Grid.PriceRange.Upper = override.Grid.PriceRange.Upper
	}
	if len(override.Grid.CustomLevels) > 0 {
		merged.Grid.CustomLevels = override.Grid.CustomLevels
	}

	// Merge Risk config
	if override.Risk.MaxDrawdown != 0 {
		merged.Risk.MaxDrawdown = override.Risk.MaxDrawdown
	}
	if override.Risk.MaxLoss != 0 {
		merged.Risk.MaxLoss = override.Risk.MaxLoss
	}
	if override.Risk.MarginBuffer != 0 {
		merged.Risk.MarginBuffer = override.Risk.MarginBuffer
	}

	// Merge stop loss config
	if override.Risk.StopLoss.PriceTrigger != 0 {
		merged.Risk.StopLoss.PriceTrigger = override.Risk.StopLoss.PriceTrigger
	}
	if override.Risk.StopLoss.PnLTrigger != 0 {
		merged.Risk.StopLoss.PnLTrigger = override.Risk.StopLoss.PnLTrigger
	}

	// Merge take profit config
	if override.Risk.TakeProfit.PriceTrigger != 0 {
		merged.Risk.TakeProfit.PriceTrigger = override.Risk.TakeProfit.PriceTrigger
	}
	if override.Risk.TakeProfit.ROIPercentage != 0 {
		merged.Risk.TakeProfit.ROIPercentage = override.Risk.TakeProfit.ROIPercentage
	}
	if override.Risk.TakeProfit.PnLTrigger != 0 {
		merged.Risk.TakeProfit.PnLTrigger = override.Risk.TakeProfit.PnLTrigger
	}

	// Merge Monitoring config
	if override.Monitoring.LogLevel != "" {
		merged.Monitoring.LogLevel = override.Monitoring.LogLevel
	}
	if override.Monitoring.LogFile != "" {
		merged.Monitoring.LogFile = override.Monitoring.LogFile
	}
	if override.Monitoring.WebhookURL != "" {
		merged.Monitoring.WebhookURL = override.Monitoring.WebhookURL
	}
	if override.Monitoring.UpdateInterval != 0 {
		merged.Monitoring.UpdateInterval = override.Monitoring.UpdateInterval
	}

	return &merged
}

// GetDefaultConfig returns a configuration with default values
func GetDefaultConfig() *Config {
	config := &Config{
		Trading: TradingConfig{
			Symbol:   "BTCUSDT",
			Leverage: DefaultLeverage,
			DryRun:   true,
		},
		Grid: GridConfig{
			Type:        GridTypeNeutral,
			Calculation: CalculationArithmetic,
			GridCount:   DefaultGridCount,
		},
		Risk: RiskConfig{
			MaxDrawdown:  DefaultMaxDrawdown,
			MarginBuffer: DefaultMarginBuffer,
		},
		Monitoring: MonitoringConfig{
			LogLevel:       "info",
			MetricsEnabled: true,
			TradeHistory:   true,
			UpdateInterval: DefaultUpdateInterval,
		},
	}

	applyDefaults(config)
	return config
}
