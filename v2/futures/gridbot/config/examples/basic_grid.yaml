# Basic Grid Trading Configuration
# This is a simple configuration for beginners

# API Configuration
api:
  api_key: "your_api_key_here"  # Will be overridden by BINANCE_API_KEY environment variable
  secret_key: "your_secret_key_here"  # Will be overridden by BINANCE_SECRET_KEY environment variable
  testnet: false  # Set to false for live API but using dry_run for safety
  # proxy: "http://127.0.0.1:7890"  # Optional proxy

# Trading Configuration
trading:
  symbol: "SHELLUSDT"
  leverage: 10  # Reduced from 20 to lower margin requirements
  initial_margin: 100.0  # USDT - set high to handle volatile market conditions
  dry_run: true  # Set to true for testing without real trades

# Grid Strategy Configuration
grid:
  type: "long"  # neutral, long, short
  calculation: "arithmetic"  # arithmetic, geometric
  price_range:
    lower: 0.1592  # Lower bound of trading range
    upper: 0.2022  # Upper bound of trading range
  grid_count: 15  # Reduced from 29 to lower margin requirements

# Risk Management Configuration
risk_management:
  # Stop Loss Configuration
  stop_loss:
    enabled: false
    price_trigger: 28000.0  # Stop if price goes below this level
    # pnl_trigger: -50.0  # Alternative: Stop if PnL drops below this amount

  # Take Profit Configuration
  take_profit:
    enabled: false
    roi_percentage: 10.0  # Take profit at 10% ROI
    # price_trigger: 40000.0  # Alternative: Take profit at specific price
    # pnl_trigger: 100.0  # Alternative: Take profit at specific PnL amount

  max_drawdown: 15.0  # Maximum allowed drawdown percentage
  margin_buffer: 15.0  # Reduced from 25% to 15% to lower margin requirements

  # Emergency Stop Configuration
  emergency:
    enabled: false
    max_consecutive_loss: 5  # Stop after 5 consecutive losses
    liquidation_buffer: 10.0  # Emergency stop if within 10% of liquidation

# Monitoring Configuration
monitoring:
  log_level: "info"  # debug, info, warn, error, fatal
  metrics_enabled: true
  trade_history: true
  update_interval: 10s  # Status update interval
  # log_file: "gridbot.log"  # Optional log file
  # webhook_url: "https://hooks.slack.com/..."  # Optional webhook for notifications

# Advanced Configuration (Optional)
advanced:
  order_timeout: 30s
  max_retries: 3
  retry_delay: 5s
  price_deviation: 0.1  # Allow 0.1% price deviation
  min_order_size: 0.001  # Minimum order size
  rebalance_interval: 5m  # Grid rebalancing interval
  health_check_interval: 30s

  # WebSocket Configuration
  websocket:
    reconnect_interval: 5s
    ping_interval: 20s
    max_reconnects: 10
    buffer_size: 1000
