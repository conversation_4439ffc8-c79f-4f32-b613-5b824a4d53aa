# Advanced Grid Trading Configuration
# This configuration demonstrates advanced features and fine-tuning

# API Configuration
api:
  api_key: "your_binance_api_key_here"
  secret_key: "your_binance_secret_key_here"
  testnet: false  # Live trading
  proxy: "http://127.0.0.1:7890"  # Using proxy

# Trading Configuration
trading:
  symbol: "ETHUSDT"
  leverage: 10
  initial_margin: 1000.0  # USDT
  dry_run: false  # Live trading

# Grid Strategy Configuration
grid:
  type: "long"  # Optimized for upward trend
  calculation: "geometric"  # Geometric progression for better distribution
  price_range:
    lower: 2000.0
    upper: 3000.0
  grid_count: 25  # More grids for finer granularity
  
  # Optional: Custom grid levels (overrides calculation)
  # custom_levels: [2000, 2050, 2100, 2200, 2300, 2500, 2700, 3000]

# Risk Management Configuration
risk_management:
  # Stop Loss Configuration
  stop_loss:
    enabled: true
    price_trigger: 1800.0  # Hard stop at this price
    pnl_trigger: -200.0  # Also stop if PnL drops below -200 USDT

  # Take Profit Configuration
  take_profit:
    enabled: true
    roi_percentage: 25.0  # Take profit at 25% ROI
    price_trigger: 3200.0  # Also take profit if price reaches this level
    pnl_trigger: 500.0  # Or if PnL reaches 500 USDT

  max_drawdown: 12.0  # Stricter drawdown limit
  max_loss: 300.0  # Maximum absolute loss allowed
  margin_buffer: 30.0  # Higher margin buffer for safety

  # Emergency Stop Configuration
  emergency:
    enabled: true
    max_consecutive_loss: 3  # More aggressive emergency stop
    liquidation_buffer: 15.0  # Stop if within 15% of liquidation

# Monitoring Configuration
monitoring:
  log_level: "debug"  # Detailed logging
  log_file: "gridbot_advanced.log"
  metrics_enabled: true
  trade_history: true
  update_interval: 5s  # More frequent updates
  webhook_url: "https://hooks.slack.com/services/YOUR/WEBHOOK/URL"

# Advanced Configuration
advanced:
  order_timeout: 45s  # Longer timeout for volatile markets
  max_retries: 5  # More retries
  retry_delay: 3s  # Faster retry
  price_deviation: 0.05  # Tighter price deviation tolerance
  min_order_size: 0.01  # Larger minimum order size
  max_order_size: 1.0  # Maximum order size limit
  rebalance_interval: 2m  # More frequent rebalancing
  health_check_interval: 15s  # More frequent health checks

  # WebSocket Configuration
  websocket:
    reconnect_interval: 3s  # Faster reconnection
    ping_interval: 15s  # More frequent pings
    max_reconnects: 20  # More reconnection attempts
    buffer_size: 2000  # Larger buffer for high-frequency data
