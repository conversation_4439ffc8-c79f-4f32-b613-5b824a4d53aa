# Testnet Configuration for Grid Trading Bot
# This configuration uses Binance testnet for safe testing

# API Configuration
api:
  api_key: "aab8ef750fcc27743c62c613f4089a8c0a47f083cd15cf4b0c0843faa6efb798"
  secret_key: "11f3f1ffcd5ef5069bef8ab5ee786572420213ca4d657cfec5b1603fb21406ca"
  testnet: true  # IMPORTANT: Use testnet for testing
  # proxy: ""  # Add proxy if needed

# Trading Configuration
trading:
  symbol: "BTCUSDT"
  leverage: 2  # Conservative leverage for testing
  initial_margin: 1000.0  # Sufficient margin for testing
  dry_run: true  # Start with dry run mode

# Grid Strategy Configuration
grid:
  type: "neutral"  # Neutral grid for ranging markets
  calculation: "arithmetic"  # Simple arithmetic progression
  price_range:
    lower: 26000.0  # Smaller range for testing
    upper: 28000.0
  grid_count: 5  # Fewer grids for testing

# Risk Management Configuration
risk_management:
  # Stop Loss Configuration
  stop_loss:
    enabled: true
    price_trigger: 23000.0  # Conservative stop loss
    pnl_trigger: -10.0  # Small loss limit for testing

  # Take Profit Configuration
  take_profit:
    enabled: true
    roi_percentage: 5.0  # Conservative profit target
    pnl_trigger: 20.0  # Take profit at modest gains

  max_drawdown: 5.0  # Very strict drawdown limit
  max_loss: 15.0  # Small maximum loss
  margin_buffer: 20.0  # Reasonable margin buffer for testing

  # Emergency Stop Configuration
  emergency:
    enabled: true
    max_consecutive_loss: 2  # Stop quickly on consecutive losses
    liquidation_buffer: 30.0  # Large liquidation buffer

# Monitoring Configuration
monitoring:
  log_level: "debug"  # Detailed logging for testing
  log_file: "gridbot_testnet.log"
  metrics_enabled: true
  trade_history: true
  update_interval: 5s  # Frequent updates for testing
  # webhook_url: ""  # Add webhook URL if needed

# Advanced Configuration
advanced:
  order_timeout: 30s
  max_retries: 2  # Fewer retries for testing
  retry_delay: 3s
  price_deviation: 0.2  # More tolerant for testing
  min_order_size: 0.001  # Small order sizes
  max_order_size: 0.01  # Cap order sizes for safety
  rebalance_interval: 2m  # More frequent rebalancing for testing
  health_check_interval: 30s

  # WebSocket Configuration
  websocket:
    reconnect_interval: 5s
    ping_interval: 20s
    max_reconnects: 5
    buffer_size: 500
