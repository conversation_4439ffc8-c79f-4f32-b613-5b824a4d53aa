# Short Grid Strategy Test Configuration
# Tests sell-heavy distribution optimized for downward trending markets

# API Configuration - Binance Testnet
api:
  api_key: "aab8ef750fcc27743c62c613f4089a8c0a47f083cd15cf4b0c0843faa6efb798"
  secret_key: "11f3f1ffcd5ef5069bef8ab5ee786572420213ca4d657cfec5b1603fb21406ca"
  testnet: true
  # proxy: ""

# Trading Configuration
trading:
  symbol: "BTCUSDT"
  leverage: 2  # Lower leverage to reduce margin requirements
  initial_margin: 29000.0  # Adequate margin for short strategy testing
  dry_run: true  # Safety first - no real orders

# Grid Strategy Configuration - SHORT
grid:
  type: "short"  # Sell-heavy distribution for downward trends
  calculation: "arithmetic"  # Equal spacing for predictable distribution
  price_range:
    lower: 117500.0  # Smaller range for testing
    upper: 119000.0  # Smaller range for testing
  grid_count: 4  # Fewer grids for lower margin requirements

# Risk Management Configuration
risk_management:
  # Stop Loss Configuration
  stop_loss:
    enabled: true
    price_trigger: 126000.0  # Higher stop loss for short strategy (price going up)
    pnl_trigger: -120.0  # Stop if PnL drops below -120 USDT

  # Take Profit Configuration
  take_profit:
    enabled: true
    roi_percentage: 10.0  # Moderate profit target for short strategy
    price_trigger: 114000.0  # Take profit if BTC drops to 114k
    pnl_trigger: 200.0  # Take profit at 200 USDT gain

  max_drawdown: 10.0  # Stricter drawdown for short strategy
  max_loss: 250.0  # Moderate maximum loss
  margin_buffer: 28.0  # Higher margin buffer for short positions

  # Emergency Stop Configuration
  emergency:
    enabled: true
    max_consecutive_loss: 3  # Conservative consecutive loss limit
    liquidation_buffer: 22.0  # Conservative liquidation buffer

# Monitoring Configuration
monitoring:
  log_level: "debug"  # Detailed logging for testing
  log_file: "short_grid_test.log"
  metrics_enabled: true
  trade_history: true
  update_interval: 3s  # Frequent updates for testing
  # webhook_url: ""  # Add webhook if needed

# Advanced Configuration
advanced:
  order_timeout: 45s
  max_retries: 3
  retry_delay: 5s
  price_deviation: 0.18  # More tolerant deviation for short strategy
  min_order_size: 0.001  # Minimum order size
  max_order_size: 0.06  # Moderate max order size for short strategy
  rebalance_interval: 3m  # Frequent rebalancing for short strategy
  health_check_interval: 30s

  # WebSocket Configuration
  websocket:
    reconnect_interval: 5s
    ping_interval: 20s
    max_reconnects: 10
    buffer_size: 1000

# Test-specific comments:
# - Strategy optimized for downward price movement
# - Expect more sell orders in upper 70% of range (118.4k-124k)
# - Expect fewer buy orders in lower 30% of range (116k-118.4k)
# - Sell order quantities should be larger than buy orders
# - Should build short position on rallies and cover on dips
