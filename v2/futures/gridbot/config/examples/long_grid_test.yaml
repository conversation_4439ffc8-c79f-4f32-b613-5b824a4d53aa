# Long Grid Strategy Test Configuration
# Tests buy-heavy distribution optimized for upward trending markets

# API Configuration - Binance Testnet
api:
  api_key: "aab8ef750fcc27743c62c613f4089a8c0a47f083cd15cf4b0c0843faa6efb798"
  secret_key: "11f3f1ffcd5ef5069bef8ab5ee786572420213ca4d657cfec5b1603fb21406ca"
  testnet: true
  # proxy: ""

# Trading Configuration
trading:
  symbol: "BTCUSDT"
  leverage: 2  # Lower leverage to reduce margin requirements
  initial_margin: 29000.0  # Higher margin for long strategy testing
  dry_run: true  # Safety first - no real orders

# Grid Strategy Configuration - LONG
grid:
  type: "long"  # Buy-heavy distribution for upward trends
  calculation: "geometric"  # Geometric spacing for better distribution
  price_range:
    lower: 117000.0  # Smaller range for testing
    upper: 119000.0  # Smaller range for testing
  grid_count: 4  # Fewer grids for lower margin requirements

# Risk Management Configuration
risk_management:
  # Stop Loss Configuration
  stop_loss:
    enabled: true
    price_trigger: 110000.0  # Lower stop loss for long strategy
    pnl_trigger: -150.0  # Stop if PnL drops below -150 USDT

  # Take Profit Configuration
  take_profit:
    enabled: true
    roi_percentage: 12.0  # Higher profit target for long strategy
    price_trigger: 125000.0  # Take profit if BTC reaches 125k
    pnl_trigger: 250.0  # Take profit at 250 USDT gain

  max_drawdown: 15.0  # Allow higher drawdown for long strategy
  max_loss: 300.0  # Higher maximum loss tolerance
  margin_buffer: 30.0  # Higher margin buffer for safety

  # Emergency Stop Configuration
  emergency:
    enabled: true
    max_consecutive_loss: 4  # Allow more losses for long strategy
    liquidation_buffer: 25.0  # Higher liquidation buffer

# Monitoring Configuration
monitoring:
  log_level: "debug"  # Detailed logging for testing
  log_file: "long_grid_test.log"
  metrics_enabled: true
  trade_history: true
  update_interval: 3s  # Frequent updates for testing
  # webhook_url: ""  # Add webhook if needed

# Advanced Configuration
advanced:
  order_timeout: 45s
  max_retries: 3
  retry_delay: 5s
  price_deviation: 0.12  # Tighter deviation for long strategy
  min_order_size: 0.001  # Minimum order size
  max_order_size: 0.08  # Larger max order size for long strategy
  rebalance_interval: 4m  # Slightly longer rebalancing
  health_check_interval: 30s

  # WebSocket Configuration
  websocket:
    reconnect_interval: 5s
    ping_interval: 20s
    max_reconnects: 10
    buffer_size: 1000

# Test-specific comments:
# - Strategy optimized for upward price movement
# - Expect more buy orders in lower 70% of range (114k-118.6k)
# - Expect fewer sell orders in upper 30% of range (118.6k-122k)
# - Buy order quantities should be larger than sell orders
# - Should accumulate position on dips and sell on rallies
