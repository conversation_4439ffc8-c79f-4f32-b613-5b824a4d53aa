package config

import (
	"os"
	"testing"
	"time"
)

func TestLoadConfig(t *testing.T) {
	// Create a temporary config file
	configContent := `
api:
  api_key: "test_api_key"
  secret_key: "test_secret_key"
  testnet: true

trading:
  symbol: "BTCUSDT"
  leverage: 5
  initial_margin: 100.0
  dry_run: true

grid:
  type: "neutral"
  calculation: "arithmetic"
  price_range:
    lower: 30000.0
    upper: 35000.0
  grid_count: 10

risk_management:
  stop_loss:
    enabled: true
    price_trigger: 28000.0
  take_profit:
    enabled: true
    roi_percentage: 15.0
  max_drawdown: 10.0
  margin_buffer: 25.0

monitoring:
  log_level: "info"
  metrics_enabled: true
  trade_history: true
  update_interval: 10s

advanced:
  order_timeout: 30s
  max_retries: 3
  retry_delay: 5s
  price_deviation: 0.1
  min_order_size: 0.001
  rebalance_interval: 5m
  health_check_interval: 30s
`

	// Write to temporary file
	tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.WriteString(configContent); err != nil {
		t.Fatalf("Failed to write config: %v", err)
	}
	tmpFile.Close()

	// Load configuration
	cfg, err := LoadConfig(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// Test API configuration
	if cfg.API.APIKey != "test_api_key" {
		t.Errorf("Expected API key 'test_api_key', got '%s'", cfg.API.APIKey)
	}
	if cfg.API.SecretKey != "test_secret_key" {
		t.Errorf("Expected secret key 'test_secret_key', got '%s'", cfg.API.SecretKey)
	}
	if !cfg.API.Testnet {
		t.Error("Expected testnet to be true")
	}

	// Test trading configuration
	if cfg.Trading.Symbol != "BTCUSDT" {
		t.Errorf("Expected symbol 'BTCUSDT', got '%s'", cfg.Trading.Symbol)
	}
	if cfg.Trading.Leverage != 5 {
		t.Errorf("Expected leverage 5, got %d", cfg.Trading.Leverage)
	}
	if cfg.Trading.InitialMargin != 100.0 {
		t.Errorf("Expected initial margin 100.0, got %f", cfg.Trading.InitialMargin)
	}
	if !cfg.Trading.DryRun {
		t.Error("Expected dry run to be true")
	}

	// Test grid configuration
	if cfg.Grid.Type != GridTypeNeutral {
		t.Errorf("Expected grid type 'neutral', got '%s'", cfg.Grid.Type)
	}
	if cfg.Grid.Calculation != CalculationArithmetic {
		t.Errorf("Expected calculation 'arithmetic', got '%s'", cfg.Grid.Calculation)
	}
	if cfg.Grid.PriceRange.Lower != 30000.0 {
		t.Errorf("Expected lower price 30000.0, got %f", cfg.Grid.PriceRange.Lower)
	}
	if cfg.Grid.PriceRange.Upper != 35000.0 {
		t.Errorf("Expected upper price 35000.0, got %f", cfg.Grid.PriceRange.Upper)
	}
	if cfg.Grid.GridCount != 10 {
		t.Errorf("Expected grid count 10, got %d", cfg.Grid.GridCount)
	}

	// Test risk management configuration
	if !cfg.Risk.StopLoss.Enabled {
		t.Error("Expected stop loss to be enabled")
	}
	if cfg.Risk.StopLoss.PriceTrigger != 28000.0 {
		t.Errorf("Expected stop loss price trigger 28000.0, got %f", cfg.Risk.StopLoss.PriceTrigger)
	}
	if !cfg.Risk.TakeProfit.Enabled {
		t.Error("Expected take profit to be enabled")
	}
	if cfg.Risk.TakeProfit.ROIPercentage != 15.0 {
		t.Errorf("Expected take profit ROI 15.0, got %f", cfg.Risk.TakeProfit.ROIPercentage)
	}
	if cfg.Risk.MaxDrawdown != 10.0 {
		t.Errorf("Expected max drawdown 10.0, got %f", cfg.Risk.MaxDrawdown)
	}
	if cfg.Risk.MarginBuffer != 25.0 {
		t.Errorf("Expected margin buffer 25.0, got %f", cfg.Risk.MarginBuffer)
	}

	// Test monitoring configuration
	if cfg.Monitoring.LogLevel != "info" {
		t.Errorf("Expected log level 'info', got '%s'", cfg.Monitoring.LogLevel)
	}
	if !cfg.Monitoring.MetricsEnabled {
		t.Error("Expected metrics to be enabled")
	}
	if !cfg.Monitoring.TradeHistory {
		t.Error("Expected trade history to be enabled")
	}
	if cfg.Monitoring.UpdateInterval != 10*time.Second {
		t.Errorf("Expected update interval 10s, got %v", cfg.Monitoring.UpdateInterval)
	}

	// Test advanced configuration
	if cfg.Advanced.OrderTimeout != 30*time.Second {
		t.Errorf("Expected order timeout 30s, got %v", cfg.Advanced.OrderTimeout)
	}
	if cfg.Advanced.MaxRetries != 3 {
		t.Errorf("Expected max retries 3, got %d", cfg.Advanced.MaxRetries)
	}
	if cfg.Advanced.RetryDelay != 5*time.Second {
		t.Errorf("Expected retry delay 5s, got %v", cfg.Advanced.RetryDelay)
	}
	if cfg.Advanced.PriceDeviation != 0.1 {
		t.Errorf("Expected price deviation 0.1, got %f", cfg.Advanced.PriceDeviation)
	}
	if cfg.Advanced.MinOrderSize != 0.001 {
		t.Errorf("Expected min order size 0.001, got %f", cfg.Advanced.MinOrderSize)
	}
	if cfg.Advanced.RebalanceInterval != 5*time.Minute {
		t.Errorf("Expected rebalance interval 5m, got %v", cfg.Advanced.RebalanceInterval)
	}
	if cfg.Advanced.HealthCheckInterval != 30*time.Second {
		t.Errorf("Expected health check interval 30s, got %v", cfg.Advanced.HealthCheckInterval)
	}
}

func TestValidateConfig(t *testing.T) {
	// Test valid configuration
	validConfig := &Config{
		API: APIConfig{
			APIKey:    "test_key",
			SecretKey: "test_secret",
		},
		Trading: TradingConfig{
			Symbol:        "BTCUSDT",
			Leverage:      5,
			InitialMargin: 100.0,
		},
		Grid: GridConfig{
			Type:        GridTypeNeutral,
			Calculation: CalculationArithmetic,
			PriceRange: PriceRange{
				Lower: 30000.0,
				Upper: 35000.0,
			},
			GridCount: 10,
		},
		Risk: RiskConfig{
			MaxDrawdown:  10.0,
			MarginBuffer: 25.0,
		},
		Monitoring: MonitoringConfig{
			LogLevel:       "info",
			UpdateInterval: 10 * time.Second,
		},
		Advanced: AdvancedConfig{
			OrderTimeout:        30 * time.Second,
			MaxRetries:          3,
			RetryDelay:          5 * time.Second,
			PriceDeviation:      0.1,
			MinOrderSize:        0.001,
			RebalanceInterval:   5 * time.Minute,
			HealthCheckInterval: 30 * time.Second,
			WebSocket: WebSocketConfig{
				ReconnectInterval: 5 * time.Second,
				PingInterval:      20 * time.Second,
				MaxReconnects:     10,
				BufferSize:        1000,
			},
		},
	}

	if err := ValidateConfig(validConfig); err != nil {
		t.Errorf("Valid configuration failed validation: %v", err)
	}

	// Test invalid configurations
	testCases := []struct {
		name   string
		modify func(*Config)
	}{
		{
			name: "empty API key",
			modify: func(c *Config) {
				c.API.APIKey = ""
			},
		},
		{
			name: "empty secret key",
			modify: func(c *Config) {
				c.API.SecretKey = ""
			},
		},
		{
			name: "empty symbol",
			modify: func(c *Config) {
				c.Trading.Symbol = ""
			},
		},
		{
			name: "invalid leverage",
			modify: func(c *Config) {
				c.Trading.Leverage = 0
			},
		},
		{
			name: "negative initial margin",
			modify: func(c *Config) {
				c.Trading.InitialMargin = -100.0
			},
		},
		{
			name: "invalid grid type",
			modify: func(c *Config) {
				c.Grid.Type = "invalid"
			},
		},
		{
			name: "invalid price range",
			modify: func(c *Config) {
				c.Grid.PriceRange.Lower = 35000.0
				c.Grid.PriceRange.Upper = 30000.0
			},
		},
		{
			name: "invalid grid count",
			modify: func(c *Config) {
				c.Grid.GridCount = 1
			},
		},
		{
			name: "invalid max drawdown",
			modify: func(c *Config) {
				c.Risk.MaxDrawdown = -10.0
			},
		},
		{
			name: "invalid log level",
			modify: func(c *Config) {
				c.Monitoring.LogLevel = "invalid"
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create a copy of valid config
			testConfig := *validConfig
			testConfig.API = validConfig.API
			testConfig.Trading = validConfig.Trading
			testConfig.Grid = validConfig.Grid
			testConfig.Risk = validConfig.Risk
			testConfig.Monitoring = validConfig.Monitoring
			testConfig.Advanced = validConfig.Advanced

			// Apply modification
			tc.modify(&testConfig)

			// Validate should fail
			if err := ValidateConfig(&testConfig); err == nil {
				t.Errorf("Expected validation to fail for %s", tc.name)
			}
		})
	}
}

func TestGetDefaultConfig(t *testing.T) {
	cfg := GetDefaultConfig()

	// Check that defaults are applied
	if cfg.Trading.Leverage != DefaultLeverage {
		t.Errorf("Expected default leverage %d, got %d", DefaultLeverage, cfg.Trading.Leverage)
	}
	if cfg.Grid.GridCount != DefaultGridCount {
		t.Errorf("Expected default grid count %d, got %d", DefaultGridCount, cfg.Grid.GridCount)
	}
	if cfg.Risk.MaxDrawdown != DefaultMaxDrawdown {
		t.Errorf("Expected default max drawdown %f, got %f", DefaultMaxDrawdown, cfg.Risk.MaxDrawdown)
	}
	if cfg.Advanced.OrderTimeout != DefaultOrderTimeout {
		t.Errorf("Expected default order timeout %v, got %v", DefaultOrderTimeout, cfg.Advanced.OrderTimeout)
	}
}
