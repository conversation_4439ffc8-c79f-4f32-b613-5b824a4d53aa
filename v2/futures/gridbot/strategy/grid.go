package strategy

import (
	"fmt"
	"sync"
	"time"

	"github.com/adshao/go-binance/v2/futures/gridbot/config"
	"github.com/adshao/go-binance/v2/futures/gridbot/market"
)

// GridStrategyImpl implements the main grid trading strategy
type GridStrategyImpl struct {
	*GridStrategy // Embed the base struct from types.go

	calculator *GridCalculator

	// Synchronization
	mu sync.RWMutex

	// Channels for communication
	eventChan   chan *GridEvent
	stopChan    chan struct{}

	// Current market condition
	currentCondition *market.MarketCondition

	// Order management
	activeOrders map[int64]*GridLevel
	orderHistory []*GridLevel

	// Additional fields for implementation
	startedAt   *time.Time
	stoppedAt   *time.Time
}

// NewGridStrategy creates a new grid trading strategy
func NewGridStrategy(cfg *config.Config) *GridStrategyImpl {
	now := time.Now()

	baseStrategy := &GridStrategy{
		Config:     cfg,
		Levels:     make([]*GridLevel, 0),
		Statistics: &StrategyStatistics{},
		Status:     StrategyStatusInitializing,
		CreatedAt:  now,
		UpdatedAt:  now,
	}

	return &GridStrategyImpl{
		GridStrategy:     baseStrategy,
		calculator:       NewGridCalculator(cfg),
		eventChan:        make(chan *GridEvent, 100),
		stopChan:         make(chan struct{}),
		activeOrders:     make(map[int64]*GridLevel),
		orderHistory:     make([]*GridLevel, 0),
	}
}

// Initialize initializes the grid strategy
func (gs *GridStrategyImpl) Initialize() error {
	gs.mu.Lock()
	defer gs.mu.Unlock()

	// Validate configuration
	if err := gs.calculator.ValidateGridConfiguration(); err != nil {
		return fmt.Errorf("configuration validation failed: %w", err)
	}

	// Calculate grid levels
	result, err := gs.calculator.CalculateGridLevels()
	if err != nil {
		return fmt.Errorf("failed to calculate grid levels: %w", err)
	}

	gs.Levels = result.Levels

	// Initialize statistics
	gs.Statistics = &StrategyStatistics{
		StartTime:      time.Now(),
		ActiveGrids:    len(gs.Levels),
		TotalTrades:    0,
		TotalPnL:       0,
		NetPnL:         0,
		MaxDrawdown:    0,
		CurrentDrawdown: 0,
		WinRate:        0,
		GridEfficiency: 0,
	}

	gs.Status = StrategyStatusActive
	gs.UpdatedAt = time.Now()

	// Send initialization event
	gs.sendEvent(GridEventType("strategy_initialized"), nil, 0, "Grid strategy initialized successfully")

	return nil
}

// Start starts the grid strategy
func (gs *GridStrategyImpl) Start() error {
	gs.mu.Lock()
	defer gs.mu.Unlock()

	if gs.Status != StrategyStatusActive && gs.Status != StrategyStatusPaused {
		return fmt.Errorf("cannot start strategy in status: %s", gs.Status)
	}

	now := time.Now()
	gs.startedAt = &now
	gs.Status = StrategyStatusActive
	gs.Statistics.StartTime = now

	// Send start event
	gs.sendEvent(GridEventType("strategy_started"), nil, 0, "Grid strategy started")

	return nil
}

// Stop stops the grid strategy
func (gs *GridStrategyImpl) Stop() error {
	gs.mu.Lock()
	defer gs.mu.Unlock()

	if gs.Status == StrategyStatusStopped {
		return nil // Already stopped
	}

	now := time.Now()
	gs.stoppedAt = &now
	gs.Status = StrategyStatusStopped

	if gs.Statistics.EndTime == nil {
		gs.Statistics.EndTime = &now
		gs.Statistics.RunDuration = now.Sub(gs.Statistics.StartTime)
	}

	// Close stop channel to signal shutdown
	select {
	case <-gs.stopChan:
		// Already closed
	default:
		close(gs.stopChan)
	}

	// Send stop event
	gs.sendEvent(GridEventType("strategy_stopped"), nil, 0, "Grid strategy stopped")

	return nil
}

// Pause pauses the grid strategy
func (gs *GridStrategyImpl) Pause() error {
	gs.mu.Lock()
	defer gs.mu.Unlock()

	if gs.Status != StrategyStatusActive {
		return fmt.Errorf("cannot pause strategy in status: %s", gs.Status)
	}

	gs.Status = StrategyStatusPaused
	gs.UpdatedAt = time.Now()

	// Send pause event
	gs.sendEvent(GridEventStrategyPaused, nil, 0, "Grid strategy paused")

	return nil
}

// Resume resumes the grid strategy
func (gs *GridStrategyImpl) Resume() error {
	gs.mu.Lock()
	defer gs.mu.Unlock()

	if gs.Status != StrategyStatusPaused {
		return fmt.Errorf("cannot resume strategy in status: %s", gs.Status)
	}

	gs.Status = StrategyStatusActive
	gs.UpdatedAt = time.Now()

	// Send resume event
	gs.sendEvent(GridEventStrategyResumed, nil, 0, "Grid strategy resumed")

	return nil
}

// Update updates the strategy with new market data
func (gs *GridStrategyImpl) Update(condition interface{}) error {
	// Type assert to market.MarketCondition
	marketCondition, ok := condition.(*market.MarketCondition)
	if !ok {
		return fmt.Errorf("invalid condition type")
	}
	gs.mu.Lock()
	defer gs.mu.Unlock()

	if gs.Status != StrategyStatusActive {
		return nil // Skip updates when not active
	}

	gs.currentCondition = marketCondition
	gs.UpdatedAt = time.Now()

	// Update statistics with current price
	gs.updatePriceStatistics(marketCondition.Price)

	// Check if price is within grid range
	if !gs.isPriceInRange(marketCondition.Price) {
		gs.sendEvent(GridEventPriceOutOfRange, nil, marketCondition.Price,
			fmt.Sprintf("Price %.2f is outside grid range [%.2f, %.2f]",
				marketCondition.Price, gs.Config.Grid.PriceRange.Lower, gs.Config.Grid.PriceRange.Upper))
		return nil
	}

	// Check for risk triggers
	if err := gs.checkRiskTriggers(marketCondition); err != nil {
		gs.sendEvent(GridEventRiskTriggered, nil, marketCondition.Price, err.Error())
		return err
	}

	return nil
}

// GetStatus returns the current strategy status
func (gs *GridStrategyImpl) GetStatus() StrategyStatus {
	gs.mu.RLock()
	defer gs.mu.RUnlock()
	return gs.Status
}

// GetStatistics returns the current strategy statistics
func (gs *GridStrategyImpl) GetStatistics() *StrategyStatistics {
	gs.mu.RLock()
	defer gs.mu.RUnlock()

	// Update runtime statistics
	stats := *gs.Statistics
	if gs.startedAt != nil {
		if gs.stoppedAt != nil {
			stats.RunDuration = gs.stoppedAt.Sub(*gs.startedAt)
		} else {
			stats.RunDuration = time.Since(*gs.startedAt)
		}
	}
	
	// Calculate derived statistics
	if stats.TotalTrades > 0 {
		stats.WinRate = float64(stats.ProfitableTrades) / float64(stats.TotalTrades) * 100
		stats.AvgTradeSize = stats.TotalVolume / float64(stats.TotalTrades)
		
		if stats.RunDuration > 0 {
			hours := stats.RunDuration.Hours()
			stats.TradesPerHour = float64(stats.TotalTrades) / hours
			stats.VolumePerHour = stats.TotalVolume / hours
		}
	}
	
	// Calculate ROI
	if gs.Config.Trading.InitialMargin > 0 {
		stats.ROI = (stats.NetPnL / gs.Config.Trading.InitialMargin) * 100
	}

	// Calculate grid efficiency
	if len(gs.Levels) > 0 {
		stats.GridEfficiency = float64(stats.FilledGrids) / float64(len(gs.Levels)) * 100
	}

	return &stats
}

// GetGridLevels returns the current grid levels
func (gs *GridStrategyImpl) GetGridLevels() []*GridLevel {
	gs.mu.RLock()
	defer gs.mu.RUnlock()

	// Return a copy to prevent external modification
	levels := make([]*GridLevel, len(gs.Levels))
	copy(levels, gs.Levels)
	return levels
}

// Validate validates the strategy configuration
func (gs *GridStrategyImpl) Validate() error {
	return gs.calculator.ValidateGridConfiguration()
}

// Helper methods

// sendEvent sends an event to the event channel
func (gs *GridStrategyImpl) sendEvent(eventType GridEventType, level *GridLevel, price float64, message string) {
	event := &GridEvent{
		Type:      eventType,
		Timestamp: time.Now(),
		GridLevel: level,
		Price:     price,
		Message:   message,
	}

	select {
	case gs.eventChan <- event:
		// Event sent successfully
	default:
		// Channel is full, skip event (non-blocking)
	}
}

// isPriceInRange checks if the price is within the grid range
func (gs *GridStrategyImpl) isPriceInRange(price float64) bool {
	return price >= gs.Config.Grid.PriceRange.Lower && price <= gs.Config.Grid.PriceRange.Upper
}

// updatePriceStatistics updates price-related statistics
func (gs *GridStrategyImpl) updatePriceStatistics(price float64) {
	if gs.Statistics.HighestPrice == 0 || price > gs.Statistics.HighestPrice {
		gs.Statistics.HighestPrice = price
	}

	if gs.Statistics.LowestPrice == 0 || price < gs.Statistics.LowestPrice {
		gs.Statistics.LowestPrice = price
	}

	gs.Statistics.CurrentPrice = price
	gs.Statistics.PriceRange = gs.Statistics.HighestPrice - gs.Statistics.LowestPrice
}

// checkRiskTriggers checks if any risk management triggers are activated
func (gs *GridStrategyImpl) checkRiskTriggers(condition *market.MarketCondition) error {
	// Check stop loss triggers
	if gs.Config.Risk.StopLoss.Enabled {
		if gs.Config.Risk.StopLoss.PriceTrigger > 0 && condition.Price <= gs.Config.Risk.StopLoss.PriceTrigger {
			return fmt.Errorf("stop loss price trigger activated: price %.2f <= trigger %.2f",
				condition.Price, gs.Config.Risk.StopLoss.PriceTrigger)
		}

		if gs.Config.Risk.StopLoss.PnLTrigger != 0 && gs.Statistics.NetPnL <= gs.Config.Risk.StopLoss.PnLTrigger {
			return fmt.Errorf("stop loss PnL trigger activated: PnL %.2f <= trigger %.2f",
				gs.Statistics.NetPnL, gs.Config.Risk.StopLoss.PnLTrigger)
		}
	}
	
	// Check take profit triggers
	if gs.Config.Risk.TakeProfit.Enabled {
		if gs.Config.Risk.TakeProfit.PriceTrigger > 0 && condition.Price >= gs.Config.Risk.TakeProfit.PriceTrigger {
			return fmt.Errorf("take profit price trigger activated: price %.2f >= trigger %.2f",
				condition.Price, gs.Config.Risk.TakeProfit.PriceTrigger)
		}

		if gs.Config.Risk.TakeProfit.PnLTrigger > 0 && gs.Statistics.NetPnL >= gs.Config.Risk.TakeProfit.PnLTrigger {
			return fmt.Errorf("take profit PnL trigger activated: PnL %.2f >= trigger %.2f",
				gs.Statistics.NetPnL, gs.Config.Risk.TakeProfit.PnLTrigger)
		}

		if gs.Config.Risk.TakeProfit.ROIPercentage > 0 && gs.Statistics.ROI >= gs.Config.Risk.TakeProfit.ROIPercentage {
			return fmt.Errorf("take profit ROI trigger activated: ROI %.2f%% >= trigger %.2f%%",
				gs.Statistics.ROI, gs.Config.Risk.TakeProfit.ROIPercentage)
		}
	}

	// Check maximum drawdown
	if gs.Statistics.CurrentDrawdown >= gs.Config.Risk.MaxDrawdown {
		return fmt.Errorf("maximum drawdown exceeded: %.2f%% >= %.2f%%",
			gs.Statistics.CurrentDrawdown, gs.Config.Risk.MaxDrawdown)
	}

	return nil
}

// Rebalance rebalances the grid based on current market conditions
func (gs *GridStrategyImpl) Rebalance(request *GridRebalanceRequest) (*GridRebalanceResult, error) {
	gs.mu.Lock()
	defer gs.mu.Unlock()

	if gs.Status != StrategyStatusActive {
		return &GridRebalanceResult{
			Success: false,
			Error:   fmt.Errorf("cannot rebalance strategy in status: %s", gs.Status),
		}, nil
	}

	// For now, return a simple success response
	// In a full implementation, this would cancel/replace orders as needed
	return &GridRebalanceResult{
		Success:         true,
		OrdersPlaced:    0,
		OrdersCancelled: 0,
		NewLevels:       gs.Levels,
		Details:         "Rebalance completed successfully",
	}, nil
}

// HandleOrderFill handles order fill events
func (gs *GridStrategyImpl) HandleOrderFill(orderID int64, fillPrice float64, fillQuantity float64) error {
	gs.mu.Lock()
	defer gs.mu.Unlock()

	// Find the grid level for this order
	for _, level := range gs.Levels {
		if level.OrderID == orderID {
			level.Status = config.GridLevelStatusFilled
			level.UpdatedAt = time.Now()
			now := time.Now()
			level.FilledAt = &now

			// Update statistics
			gs.Statistics.TotalTrades++
			gs.Statistics.FilledGrids++

			break
		}
	}

	return nil
}

// CalculateRequiredMargin calculates the total margin required for the strategy
func (gs *GridStrategyImpl) CalculateRequiredMargin() (float64, error) {
	gs.mu.RLock()
	defer gs.mu.RUnlock()

	result, err := gs.calculator.CalculateGridLevels()
	if err != nil {
		return 0, fmt.Errorf("failed to calculate grid levels: %w", err)
	}

	return result.RequiredMargin, nil
}
